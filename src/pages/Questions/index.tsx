import { type ColumnDef } from "@tanstack/react-table";
import { CustomTable } from "@/components/CustomTable";

interface Payment {
  id: string;
  amount: number;
  status: "pending" | "processing" | "success" | "failed";
  email: string;
}

export const PaymentTable = () => {
  const data: Payment[] = [
    {
      id: "m5gr84i9",
      amount: 316,
      status: "success",
      email:
        "lorem ipsum dolor sit amet consectetur adipiscing elit sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.",
    },
    // ... other data
  ];

  const columns: ColumnDef<Payment>[] = [
    {
      accessorKey: "status",
      header: "Loại câu hỏi",
      cell: ({ row }) => (
        <div className="capitalize text-start">{row.getValue("status")}</div>
      ),
    },
    {
      accessorKey: "status",
      header: "Câu hỏi tiếng Anh",
      cell: ({ row }) => (
        <div className="capitalize text-start">{row.getValue("email")}</div>
      ),
      meta: {
        className: "w-[200px]", // Sử dụng Tailwind CSS
      },
    },
    {
      accessorKey: "email",
      header: "Câu hỏi tiếng Việt",
      cell: ({ row }) => (
        <div className="lowercase text-start">{row.getValue("email")}</div>
      ),
      size: 300,
    },
    {
      accessorKey: "email",
      header: "Ví dụ (Tiếng Anh)",
      cell: ({ row }) => (
        <div className="lowercase text-start">{row.getValue("email")}</div>
      ),
    },
    {
      accessorKey: "email",
      header: "Ví dụ (Tiếng Việt)",
      cell: ({ row }) => (
        <div className="lowercase text-start">{row.getValue("email")}</div>
      ),
    },
  ];

  return (
    <CustomTable<Payment>
      data={data}
      columns={columns}
      title="Payments"
      description="List of all payments processed"
      searchPlaceholder="Filter payments..."
      searchColumn="email"
    />
  );
};
